// uniCloud云函数：测试GPT翻译功能并进行网络诊断
// 参考 subtitle-translation-gpt 结构，专门用于测试和诊断网络问题
"use strict";

const createConfig = require("uni-config-center");

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-3.5-turbo",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 1000,
  API_TIMEOUT: 60000 * 3, // 3分钟超时，比原来更长
  TEST_TIMEOUT: 30000, // 测试用的较短超时
};

/**
 * 云函数入口
 * @param {Object} event - 事件参数
 * @param {string} event.mode - 运行模式: 'test' | 'diagnosis' | 'translate'
 * @param {string} event.text - 要翻译的文本（translate模式）
 * @param {string} event.targetLanguage - 目标语言（translate模式）
 * @returns {Object} 测试结果
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('🧪 开始测试翻译功能...', { mode: event.mode || 'test' });

  try {
    const mode = event.mode || 'test';
    
    switch (mode) {
      case 'diagnosis':
        return await runNetworkDiagnosis(context);
      case 'translate':
        return await runTranslationTest(event, context);
      case 'test':
      default:
        return await runComprehensiveTest(event, context);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message,
      duration: Date.now() - startTime
    };
  }
};

/**
 * 运行网络诊断
 */
async function runNetworkDiagnosis(context) {
  console.log('🔍 开始网络诊断...');
  
  const results = {
    timestamp: new Date().toISOString(),
    environment: {
      region: context.REGION || 'unknown',
      functionName: context.FUNCTION_NAME,
      requestId: context.REQUEST_ID,
      nodeVersion: process.version,
      platform: process.platform
    },
    tests: {}
  };

  // 1. DNS解析测试
  try {
    console.log('📡 测试DNS解析...');
    const dns = require('dns').promises;
    const dnsStart = Date.now();
    const addresses = await dns.resolve4('aihubmix.com');
    const dnsTime = Date.now() - dnsStart;
    
    results.tests.dns = {
      success: true,
      addresses,
      responseTime: dnsTime,
      message: `DNS解析成功: ${addresses.join(', ')} (${dnsTime}ms)`
    };
    console.log('✅ DNS解析成功:', addresses);
  } catch (dnsError) {
    results.tests.dns = {
      success: false,
      error: dnsError.message,
      message: `DNS解析失败: ${dnsError.message}`
    };
    console.error('❌ DNS解析失败:', dnsError.message);
  }

  // 2. HTTP连接测试
  try {
    console.log('📡 测试HTTP连接...');
    const httpStart = Date.now();
    const response = await uniCloud.httpclient.request('https://aihubmix.com', {
      method: 'GET',
      timeout: CONFIG.TEST_TIMEOUT,
      headers: {
        'User-Agent': 'uniCloud-test-translation/1.0'
      }
    });
    const httpTime = Date.now() - httpStart;
    
    results.tests.http = {
      success: true,
      statusCode: response.status,
      responseTime: httpTime,
      message: `HTTP连接成功，状态码: ${response.status} (${httpTime}ms)`
    };
    console.log('✅ HTTP连接成功:', response.status);
  } catch (httpError) {
    results.tests.http = {
      success: false,
      error: httpError.message,
      message: `HTTP连接失败: ${httpError.message}`
    };
    console.error('❌ HTTP连接失败:', httpError.message);
  }

  // 3. API端点测试
  try {
    console.log('📡 测试API端点...');
    const apiStart = Date.now();
    const apiResponse = await uniCloud.httpclient.request('https://aihubmix.com/v1/chat/completions', {
      method: 'POST',
      timeout: CONFIG.TEST_TIMEOUT,
      headers: {
        'User-Agent': 'uniCloud-test-translation/1.0',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key'
      },
      data: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'test' }]
      })
    });
    const apiTime = Date.now() - apiStart;
    
    results.tests.api = {
      success: true,
      statusCode: apiResponse.status,
      responseTime: apiTime,
      message: `API端点响应，状态码: ${apiResponse.status} (${apiTime}ms)`
    };
    console.log('✅ API端点响应:', apiResponse.status);
  } catch (apiError) {
    results.tests.api = {
      success: false,
      error: apiError.message,
      message: `API端点测试失败: ${apiError.message}`
    };
    console.error('❌ API端点测试失败:', apiError.message);
  }

  return {
    success: true,
    data: results,
    summary: generateDiagnosisSummary(results)
  };
}

/**
 * 运行翻译测试
 */
async function runTranslationTest(event, context) {
  console.log('🌐 开始翻译测试...');
  
  const { text = "Hello, this is a test.", targetLanguage = "zh" } = event;
  
  try {
    // 获取API配置
    const { apiKey, baseUrl, model } = await getAndValidateGptConfig();
    
    // 执行翻译
    const translationStart = Date.now();
    const result = await performTranslation(text, targetLanguage, apiKey, baseUrl, model);
    const translationTime = Date.now() - translationStart;
    
    return {
      success: true,
      data: {
        originalText: text,
        translatedText: result.translatedText,
        targetLanguage,
        model,
        responseTime: translationTime,
        apiResponse: result.apiResponse
      },
      message: `翻译成功，耗时: ${translationTime}ms`
    };
    
  } catch (error) {
    console.error('❌ 翻译测试失败:', error);
    return {
      success: false,
      error: error.message,
      details: {
        originalText: text,
        targetLanguage,
        errorType: error.name || 'TranslationError'
      }
    };
  }
}

/**
 * 运行综合测试
 */
async function runComprehensiveTest(event, context) {
  console.log('🔬 开始综合测试...');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  };
  
  // 1. 网络诊断
  console.log('📊 步骤1: 网络诊断');
  const diagnosisResult = await runNetworkDiagnosis(context);
  results.tests.diagnosis = diagnosisResult;
  
  // 2. 翻译测试
  console.log('📊 步骤2: 翻译测试');
  const translationResult = await runTranslationTest({
    text: "This is a comprehensive test of the translation system.",
    targetLanguage: "zh"
  }, context);
  results.tests.translation = translationResult;
  
  // 3. 性能测试
  console.log('📊 步骤3: 性能测试');
  const performanceResult = await runPerformanceTest(context);
  results.tests.performance = performanceResult;
  
  return {
    success: true,
    data: results,
    summary: generateComprehensiveSummary(results)
  };
}

/**
 * 获取和验证GPT API配置
 */
async function getAndValidateGptConfig() {
  const gptConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.DEFAULT_MODEL,
    },
  });

  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");
  const model = gptConfig.config("model");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  console.log('🔑 API配置验证成功', { baseUrl, model, hasApiKey: !!apiKey });
  return { apiKey, baseUrl, model };
}

/**
 * 执行翻译
 */
async function performTranslation(text, targetLanguage, apiKey, baseUrl, model) {
  const targetLangName = targetLanguage === 'zh' ? '中文' : '英文';
  
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位专业的翻译专家。请将用户提供的文本翻译成${targetLangName}，要求准确、流畅、自然。`
      },
      {
        role: "user",
        content: `请将以下文本翻译成${targetLangName}：\n\n${text}`
      }
    ],
    temperature: CONFIG.TEMPERATURE,
    max_tokens: CONFIG.MAX_TOKENS,
  };

  console.log('📡 发送翻译请求', {
    model,
    textLength: text.length,
    targetLanguage: targetLangName,
    baseUrl
  });

  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(requestBody),
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  if (response.status !== 200) {
    throw new Error(`API请求失败，状态码: ${response.status}`);
  }

  const result = response.data;
  if (!result.choices || !result.choices[0] || !result.choices[0].message) {
    throw new Error("API响应格式异常");
  }

  const translatedText = result.choices[0].message.content.trim();
  
  return {
    translatedText,
    apiResponse: {
      model: result.model,
      usage: result.usage,
      finishReason: result.choices[0].finish_reason
    }
  };
}

/**
 * 运行性能测试
 */
async function runPerformanceTest(context) {
  console.log('⚡ 开始性能测试...');
  
  const tests = [];
  const testTexts = [
    "Short test.",
    "This is a medium length test sentence for performance evaluation.",
    "This is a much longer test sentence that contains more words and should take longer to process, allowing us to evaluate the performance characteristics of the translation system under different load conditions."
  ];
  
  for (let i = 0; i < testTexts.length; i++) {
    const text = testTexts[i];
    try {
      const start = Date.now();
      const result = await runTranslationTest({ text, targetLanguage: "zh" }, context);
      const duration = Date.now() - start;
      
      tests.push({
        testCase: `性能测试${i + 1}`,
        textLength: text.length,
        success: result.success,
        duration,
        throughput: text.length / (duration / 1000) // 字符/秒
      });
    } catch (error) {
      tests.push({
        testCase: `性能测试${i + 1}`,
        textLength: text.length,
        success: false,
        error: error.message,
        duration: 0
      });
    }
  }
  
  return {
    success: true,
    tests,
    summary: {
      totalTests: tests.length,
      successfulTests: tests.filter(t => t.success).length,
      averageDuration: tests.reduce((sum, t) => sum + t.duration, 0) / tests.length,
      averageThroughput: tests.filter(t => t.success).reduce((sum, t) => sum + (t.throughput || 0), 0) / tests.filter(t => t.success).length
    }
  };
}

/**
 * 生成诊断摘要
 */
function generateDiagnosisSummary(results) {
  const tests = results.tests;
  const summary = {
    totalTests: Object.keys(tests).length,
    successfulTests: Object.values(tests).filter(t => t.success).length,
    issues: [],
    recommendations: []
  };
  
  if (!tests.dns?.success) {
    summary.issues.push('DNS解析失败');
    summary.recommendations.push('检查网络连接或使用备用域名');
  }
  
  if (!tests.http?.success) {
    summary.issues.push('HTTP连接失败');
    summary.recommendations.push('检查防火墙设置或网络策略');
  }
  
  if (!tests.api?.success) {
    summary.issues.push('API端点无法访问');
    summary.recommendations.push('检查API密钥配置或服务状态');
  }
  
  if (summary.issues.length === 0) {
    summary.recommendations.push('网络连接正常，可以进行翻译测试');
  }
  
  return summary;
}

/**
 * 生成综合测试摘要
 */
function generateComprehensiveSummary(results) {
  const tests = results.tests;
  return {
    networkStatus: tests.diagnosis?.success ? 'healthy' : 'issues',
    translationStatus: tests.translation?.success ? 'working' : 'failed',
    performanceStatus: tests.performance?.success ? 'good' : 'poor',
    overallHealth: (tests.diagnosis?.success && tests.translation?.success && tests.performance?.success) ? 'excellent' : 'needs_attention',
    recommendations: [
      ...(tests.diagnosis?.summary?.recommendations || []),
      ...(tests.performance?.success ? ['系统性能良好'] : ['建议优化性能配置'])
    ]
  };
}
