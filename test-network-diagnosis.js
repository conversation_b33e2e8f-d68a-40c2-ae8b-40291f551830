/**
 * 网络诊断测试脚本
 * 用于测试云函数环境访问aihubmix.com的网络状况
 */

const uniCloud = require('@dcloudio/unicloud');

// 初始化uniCloud
uniCloud.init({
  provider: 'aliyun',
  spaceId: 'mp-f9e7e176-4f8c-4a8f-8c1a-2b3c4d5e6f7g', // 请替换为你的实际spaceId
  clientSecret: 'your-client-secret' // 请替换为你的实际clientSecret
});

async function runNetworkDiagnosis() {
  console.log('🔍 开始网络诊断测试...');
  
  try {
    const result = await uniCloud.callFunction({
      name: 'network-diagnosis',
      data: {
        domain: 'aihubmix.com'
      }
    });
    
    console.log('📊 诊断结果:');
    console.log(JSON.stringify(result.result, null, 2));
    
    // 分析结果
    const data = result.result.data;
    console.log('\n📋 诊断分析:');
    
    // DNS分析
    if (data.tests.dns) {
      if (data.tests.dns.success) {
        console.log(`✅ DNS解析正常: ${data.tests.dns.addresses.join(', ')} (${data.tests.dns.responseTime}ms)`);
      } else {
        console.log(`❌ DNS解析失败: ${data.tests.dns.error}`);
      }
    }
    
    // HTTP连接分析
    const httpTests = Object.keys(data.tests).filter(key => key.startsWith('http_'));
    if (httpTests.length > 0) {
      console.log('\n🌐 HTTP连接测试结果:');
      httpTests.forEach(testKey => {
        const test = data.tests[testKey];
        if (test.success) {
          console.log(`✅ ${testKey}: ${test.responseTime}ms (状态码: ${test.statusCode})`);
        } else {
          console.log(`❌ ${testKey}: ${test.error}`);
        }
      });
    }
    
    // API端点分析
    const apiTests = Object.keys(data.tests).filter(key => key.startsWith('api_'));
    if (apiTests.length > 0) {
      console.log('\n🔌 API端点测试结果:');
      apiTests.forEach(testKey => {
        const test = data.tests[testKey];
        if (test.success) {
          console.log(`✅ ${test.endpoint}: ${test.responseTime}ms (状态码: ${test.statusCode})`);
        } else {
          console.log(`❌ ${test.endpoint}: ${test.error}`);
        }
      });
    }
    
    // TCP连接分析
    if (data.tests.tcp_connect) {
      if (data.tests.tcp_connect.success) {
        console.log(`✅ TCP连接正常: ${data.tests.tcp_connect.responseTime}ms`);
      } else {
        console.log(`❌ TCP连接失败: ${data.tests.tcp_connect.error}`);
      }
    }
    
    // 环境信息
    console.log('\n🏗️ 云函数环境信息:');
    console.log(`- 区域: ${data.environment.region}`);
    console.log(`- Node版本: ${data.environment.nodeVersion}`);
    console.log(`- 平台: ${data.environment.platform}`);
    
    // 给出建议
    console.log('\n💡 建议:');
    if (data.tests.dns && !data.tests.dns.success) {
      console.log('- DNS解析失败，可能需要使用备用域名或直接IP地址');
    }
    
    const successfulHttpTests = httpTests.filter(key => data.tests[key].success);
    if (successfulHttpTests.length === 0) {
      console.log('- 所有HTTP连接都失败，可能是网络策略限制或域名被屏蔽');
    } else if (successfulHttpTests.length < httpTests.length) {
      console.log('- 部分HTTP连接超时，建议增加超时时间或使用重试机制');
    }
    
    const successfulApiTests = apiTests.filter(key => data.tests[key].success);
    if (successfulApiTests.length === 0) {
      console.log('- API端点全部无法访问，可能需要检查API服务状态或使用代理');
    }
    
  } catch (error) {
    console.error('❌ 诊断测试失败:', error);
  }
}

// 运行诊断
if (require.main === module) {
  runNetworkDiagnosis().then(() => {
    console.log('\n🎉 诊断完成');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 诊断过程出错:', error);
    process.exit(1);
  });
}

module.exports = { runNetworkDiagnosis };
