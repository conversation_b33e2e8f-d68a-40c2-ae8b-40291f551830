'use strict';

/**
 * 网络诊断云函数
 * 用于诊断云函数环境访问外部服务的网络状况
 */

exports.main = async (event, context) => {
  console.log('开始网络诊断...');

  const targetDomain = event.domain || 'aihubmix.com';
  const diagnosticResults = {
    domain: targetDomain,
    timestamp: new Date().toISOString(),
    tests: {},
    environment: {
      region: context.REGION || 'unknown',
      functionName: context.FUNCTION_NAME,
      requestId: context.REQUEST_ID,
      cloudProvider: 'aliyun',
      nodeVersion: process.version,
      platform: process.platform
    }
  };

  try {
    // 1. DNS解析测试
    console.log('🔍 开始DNS解析测试...');
    const dns = require('dns').promises;
    try {
      const dnsStart = Date.now();
      const addresses = await dns.resolve4(targetDomain);
      const dnsTime = Date.now() - dnsStart;

      diagnosticResults.tests.dns = {
        success: true,
        addresses,
        responseTime: dnsTime,
        message: `DNS解析成功，耗时${dnsTime}ms`
      };
      console.log('✅ DNS解析成功:', addresses);
    } catch (dnsError) {
      diagnosticResults.tests.dns = {
        success: false,
        error: dnsError.message,
        message: 'DNS解析失败'
      };
      console.error('❌ DNS解析失败:', dnsError.message);
    }
    
    // 2. 多种超时时间的HTTP连接测试
    console.log('🔍 开始HTTP连接测试...');
    const timeouts = [5000, 10000, 30000]; // 5秒、10秒、30秒

    for (let i = 0; i < timeouts.length; i++) {
      const timeout = timeouts[i];
      try {
        console.log(`🔍 尝试${timeout/1000}秒超时的HTTP连接...`);
        const httpStart = Date.now();
        const response = await uniCloud.httpclient.request(`https://${targetDomain}`, {
          method: 'GET',
          timeout: timeout,
          headers: {
            'User-Agent': 'uniCloud-diagnostic/1.0',
            'Accept': '*/*',
            'Connection': 'close'
          }
        });
        const httpTime = Date.now() - httpStart;

        diagnosticResults.tests[`http_${timeout}ms`] = {
          success: true,
          statusCode: response.status,
          responseTime: httpTime,
          timeout: timeout,
          headers: response.headers,
          message: `HTTP连接成功，状态码${response.status}，耗时${httpTime}ms (超时设置${timeout}ms)`
        };
        console.log(`✅ HTTP连接成功 (${timeout}ms超时):`, response.status);
        break; // 成功后跳出循环
      } catch (httpError) {
        diagnosticResults.tests[`http_${timeout}ms`] = {
          success: false,
          error: httpError.message,
          timeout: timeout,
          message: `HTTP连接失败 (超时设置${timeout}ms): ${httpError.message}`
        };
        console.error(`❌ HTTP连接失败 (${timeout}ms超时):`, httpError.message);
      }
    }
    
    // 3. API端点测试 - 测试实际使用的API路径
    console.log('🔍 开始API端点测试...');
    const apiEndpoints = [
      '/v1/models',
      '/v1/chat/completions',
      '/v1/audio/transcriptions'
    ];

    for (const endpoint of apiEndpoints) {
      try {
        console.log(`🔍 测试API端点: ${endpoint}`);
        const apiStart = Date.now();
        const apiResponse = await uniCloud.httpclient.request(`https://${targetDomain}${endpoint}`, {
          method: 'GET',
          timeout: 20000,
          headers: {
            'User-Agent': 'uniCloud-diagnostic/1.0',
            'Accept': 'application/json',
            'Authorization': 'Bearer test-key' // 测试用的key
          }
        });
        const apiTime = Date.now() - apiStart;

        diagnosticResults.tests[`api_${endpoint.replace(/[\/]/g, '_')}`] = {
          success: true,
          statusCode: apiResponse.status,
          responseTime: apiTime,
          endpoint: endpoint,
          message: `API端点${endpoint}测试成功，状态码${apiResponse.status}，耗时${apiTime}ms`
        };
        console.log(`✅ API端点${endpoint}测试成功:`, apiResponse.status);
      } catch (apiError) {
        diagnosticResults.tests[`api_${endpoint.replace(/[\/]/g, '_')}`] = {
          success: false,
          error: apiError.message,
          endpoint: endpoint,
          message: `API端点${endpoint}测试失败: ${apiError.message}`
        };
        console.error(`❌ API端点${endpoint}测试失败:`, apiError.message);
      }
    }

    // 4. 网络路由追踪（简化版）
    console.log('🔍 开始网络路由信息收集...');
    try {
      const net = require('net');
      const testSocket = new net.Socket();
      const connectStart = Date.now();

      await new Promise((resolve, reject) => {
        testSocket.setTimeout(10000);
        testSocket.connect(443, targetDomain, () => {
          const connectTime = Date.now() - connectStart;
          diagnosticResults.tests.tcp_connect = {
            success: true,
            responseTime: connectTime,
            message: `TCP连接成功，耗时${connectTime}ms`
          };
          testSocket.destroy();
          resolve();
        });

        testSocket.on('error', (err) => {
          diagnosticResults.tests.tcp_connect = {
            success: false,
            error: err.message,
            message: `TCP连接失败: ${err.message}`
          };
          reject(err);
        });

        testSocket.on('timeout', () => {
          diagnosticResults.tests.tcp_connect = {
            success: false,
            error: 'Connection timeout',
            message: 'TCP连接超时'
          };
          testSocket.destroy();
          reject(new Error('Connection timeout'));
        });
      });
    } catch (tcpError) {
      console.error('❌ TCP连接测试失败:', tcpError.message);
    }
    
    console.log('📊 诊断完成:', JSON.stringify(diagnosticResults, null, 2));
    
    return {
      success: true,
      data: diagnosticResults
    };
    
  } catch (error) {
    console.error('❌ 诊断过程出错:', error);
    return {
      success: false,
      error: error.message,
      data: diagnosticResults
    };
  }
};
