'use strict';

/**
 * 网络诊断云函数
 * 用于诊断云函数环境访问外部服务的网络状况
 */

exports.main = async (event, context) => {
  console.log('开始网络诊断...');
  
  const targetDomain = event.domain || 'aihubmix.com';
  const diagnosticResults = {
    domain: targetDomain,
    timestamp: new Date().toISOString(),
    tests: {}
  };
  
  try {
    // 1. DNS解析测试
    console.log('🔍 开始DNS解析测试...');
    const dns = require('dns').promises;
    try {
      const dnsStart = Date.now();
      const addresses = await dns.resolve4(targetDomain);
      const dnsTime = Date.now() - dnsStart;
      
      diagnosticResults.tests.dns = {
        success: true,
        addresses,
        responseTime: dnsTime,
        message: `DNS解析成功，耗时${dnsTime}ms`
      };
      console.log('✅ DNS解析成功:', addresses);
    } catch (dnsError) {
      diagnosticResults.tests.dns = {
        success: false,
        error: dnsError.message,
        message: 'DNS解析失败'
      };
      console.error('❌ DNS解析失败:', dnsError.message);
    }
    
    // 2. HTTP连接测试
    console.log('🔍 开始HTTP连接测试...');
    try {
      const httpStart = Date.now();
      const response = await uniCloud.httpclient.request(`https://${targetDomain}`, {
        method: 'GET',
        timeout: 10000,
        headers: {
          'User-Agent': 'uniCloud-diagnostic/1.0'
        }
      });
      const httpTime = Date.now() - httpStart;
      
      diagnosticResults.tests.http = {
        success: true,
        statusCode: response.status,
        responseTime: httpTime,
        headers: response.headers,
        message: `HTTP连接成功，状态码${response.status}，耗时${httpTime}ms`
      };
      console.log('✅ HTTP连接成功:', response.status);
    } catch (httpError) {
      diagnosticResults.tests.http = {
        success: false,
        error: httpError.message,
        message: 'HTTP连接失败'
      };
      console.error('❌ HTTP连接失败:', httpError.message);
    }
    
    // 3. API端点测试
    console.log('🔍 开始API端点测试...');
    try {
      const apiStart = Date.now();
      const apiResponse = await uniCloud.httpclient.request(`https://${targetDomain}/v1/models`, {
        method: 'GET',
        timeout: 15000,
        headers: {
          'User-Agent': 'uniCloud-diagnostic/1.0',
          'Accept': 'application/json'
        }
      });
      const apiTime = Date.now() - apiStart;
      
      diagnosticResults.tests.api = {
        success: true,
        statusCode: apiResponse.status,
        responseTime: apiTime,
        message: `API端点测试成功，耗时${apiTime}ms`
      };
      console.log('✅ API端点测试成功');
    } catch (apiError) {
      diagnosticResults.tests.api = {
        success: false,
        error: apiError.message,
        message: 'API端点测试失败'
      };
      console.error('❌ API端点测试失败:', apiError.message);
    }
    
    // 4. 网络环境信息
    diagnosticResults.environment = {
      region: context.REGION || 'unknown',
      functionName: context.FUNCTION_NAME,
      requestId: context.REQUEST_ID,
      cloudProvider: 'aliyun'
    };
    
    console.log('📊 诊断完成:', JSON.stringify(diagnosticResults, null, 2));
    
    return {
      success: true,
      data: diagnosticResults
    };
    
  } catch (error) {
    console.error('❌ 诊断过程出错:', error);
    return {
      success: false,
      error: error.message,
      data: diagnosticResults
    };
  }
};
