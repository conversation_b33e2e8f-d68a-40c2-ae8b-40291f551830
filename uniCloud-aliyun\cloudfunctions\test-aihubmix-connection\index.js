'use strict';

/**
 * 测试aihubmix.com连接的云函数
 * 专门用于排查网络连接问题
 */

exports.main = async (event, context) => {
  console.log('🔍 开始测试aihubmix.com连接...');
  
  const results = {
    timestamp: new Date().toISOString(),
    environment: {
      region: context.REGION || 'unknown',
      functionName: context.FUNCTION_NAME,
      requestId: context.REQUEST_ID
    },
    tests: []
  };
  
  // 测试1: 基础HTTP连接
  console.log('📡 测试1: 基础HTTP连接');
  try {
    const start1 = Date.now();
    const response1 = await uniCloud.httpclient.request('https://aihubmix.com', {
      method: 'GET',
      timeout: 10000,
      headers: {
        'User-Agent': 'uniCloud-test/1.0'
      }
    });
    const time1 = Date.now() - start1;
    
    results.tests.push({
      test: '基础HTTP连接',
      success: true,
      statusCode: response1.status,
      responseTime: time1,
      message: `连接成功，状态码: ${response1.status}，耗时: ${time1}ms`
    });
    console.log(`✅ 基础HTTP连接成功: ${response1.status} (${time1}ms)`);
  } catch (error1) {
    results.tests.push({
      test: '基础HTTP连接',
      success: false,
      error: error1.message,
      message: `连接失败: ${error1.message}`
    });
    console.error('❌ 基础HTTP连接失败:', error1.message);
  }
  
  // 测试2: API端点连接 - /v1/models
  console.log('📡 测试2: API端点连接 (/v1/models)');
  try {
    const start2 = Date.now();
    const response2 = await uniCloud.httpclient.request('https://aihubmix.com/v1/models', {
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'uniCloud-test/1.0',
        'Accept': 'application/json'
      }
    });
    const time2 = Date.now() - start2;
    
    results.tests.push({
      test: 'API端点(/v1/models)',
      success: true,
      statusCode: response2.status,
      responseTime: time2,
      message: `API端点连接成功，状态码: ${response2.status}，耗时: ${time2}ms`
    });
    console.log(`✅ API端点连接成功: ${response2.status} (${time2}ms)`);
  } catch (error2) {
    results.tests.push({
      test: 'API端点(/v1/models)',
      success: false,
      error: error2.message,
      message: `API端点连接失败: ${error2.message}`
    });
    console.error('❌ API端点连接失败:', error2.message);
  }
  
  // 测试3: 模拟实际API调用 - Whisper
  console.log('📡 测试3: 模拟Whisper API调用');
  try {
    const start3 = Date.now();
    const response3 = await uniCloud.httpclient.request('https://aihubmix.com/v1/audio/transcriptions', {
      method: 'POST',
      timeout: 20000,
      headers: {
        'User-Agent': 'uniCloud-test/1.0',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key'
      },
      data: JSON.stringify({
        model: 'whisper-1',
        file: 'test'
      })
    });
    const time3 = Date.now() - start3;
    
    results.tests.push({
      test: 'Whisper API模拟调用',
      success: true,
      statusCode: response3.status,
      responseTime: time3,
      message: `Whisper API响应，状态码: ${response3.status}，耗时: ${time3}ms`
    });
    console.log(`✅ Whisper API响应: ${response3.status} (${time3}ms)`);
  } catch (error3) {
    results.tests.push({
      test: 'Whisper API模拟调用',
      success: false,
      error: error3.message,
      message: `Whisper API调用失败: ${error3.message}`
    });
    console.error('❌ Whisper API调用失败:', error3.message);
  }
  
  // 测试4: 模拟实际API调用 - GPT
  console.log('📡 测试4: 模拟GPT API调用');
  try {
    const start4 = Date.now();
    const response4 = await uniCloud.httpclient.request('https://aihubmix.com/v1/chat/completions', {
      method: 'POST',
      timeout: 20000,
      headers: {
        'User-Agent': 'uniCloud-test/1.0',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-key'
      },
      data: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'test' }]
      })
    });
    const time4 = Date.now() - start4;
    
    results.tests.push({
      test: 'GPT API模拟调用',
      success: true,
      statusCode: response4.status,
      responseTime: time4,
      message: `GPT API响应，状态码: ${response4.status}，耗时: ${time4}ms`
    });
    console.log(`✅ GPT API响应: ${response4.status} (${time4}ms)`);
  } catch (error4) {
    results.tests.push({
      test: 'GPT API模拟调用',
      success: false,
      error: error4.message,
      message: `GPT API调用失败: ${error4.message}`
    });
    console.error('❌ GPT API调用失败:', error4.message);
  }
  
  // 分析结果
  const successCount = results.tests.filter(t => t.success).length;
  const totalCount = results.tests.length;
  
  console.log(`📊 测试完成: ${successCount}/${totalCount} 个测试成功`);
  
  // 生成建议
  const suggestions = [];
  if (successCount === 0) {
    suggestions.push('所有连接都失败，可能是网络策略限制或域名无法访问');
    suggestions.push('建议检查云函数的网络出口策略');
    suggestions.push('考虑使用备用域名或代理服务');
  } else if (successCount < totalCount) {
    suggestions.push('部分连接失败，可能是超时或API端点问题');
    suggestions.push('建议增加超时时间或添加重试机制');
  } else {
    suggestions.push('所有连接都成功，网络连接正常');
    suggestions.push('如果实际使用中仍有问题，可能是特定API调用的问题');
  }
  
  return {
    success: true,
    data: {
      ...results,
      summary: {
        successCount,
        totalCount,
        successRate: `${Math.round(successCount / totalCount * 100)}%`
      },
      suggestions
    }
  };
};
