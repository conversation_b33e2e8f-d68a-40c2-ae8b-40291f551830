/**
 * 本地网络测试脚本
 * 用于对比本地环境和云函数环境访问aihubmix.com的性能差异
 */

const https = require('https');
const http = require('http');
const dns = require('dns').promises;

async function testLocalConnection() {
  console.log('🏠 开始本地网络测试...');
  
  const results = {
    timestamp: new Date().toISOString(),
    environment: 'local',
    tests: []
  };
  
  // 测试1: DNS解析
  console.log('📡 测试1: DNS解析');
  try {
    const dnsStart = Date.now();
    const addresses = await dns.resolve4('aihubmix.com');
    const dnsTime = Date.now() - dnsStart;
    
    results.tests.push({
      test: 'DNS解析',
      success: true,
      addresses,
      responseTime: dnsTime,
      message: `DNS解析成功: ${addresses.join(', ')} (${dnsTime}ms)`
    });
    console.log(`✅ DNS解析成功: ${addresses.join(', ')} (${dnsTime}ms)`);
  } catch (dnsError) {
    results.tests.push({
      test: 'DNS解析',
      success: false,
      error: dnsError.message,
      message: `DNS解析失败: ${dnsError.message}`
    });
    console.error('❌ DNS解析失败:', dnsError.message);
  }
  
  // 测试2: 基础HTTP连接
  console.log('📡 测试2: 基础HTTP连接');
  try {
    const httpTime = await testHttpRequest('https://aihubmix.com', 'GET');
    results.tests.push({
      test: '基础HTTP连接',
      success: true,
      responseTime: httpTime,
      message: `HTTP连接成功 (${httpTime}ms)`
    });
    console.log(`✅ HTTP连接成功 (${httpTime}ms)`);
  } catch (httpError) {
    results.tests.push({
      test: '基础HTTP连接',
      success: false,
      error: httpError.message,
      message: `HTTP连接失败: ${httpError.message}`
    });
    console.error('❌ HTTP连接失败:', httpError.message);
  }
  
  // 测试3: API端点测试
  const apiEndpoints = [
    '/v1/models',
    '/v1/chat/completions',
    '/v1/audio/transcriptions'
  ];
  
  for (const endpoint of apiEndpoints) {
    console.log(`📡 测试API端点: ${endpoint}`);
    try {
      const apiTime = await testHttpRequest(`https://aihubmix.com${endpoint}`, 'GET', {
        'Accept': 'application/json',
        'Authorization': 'Bearer test-key'
      });
      results.tests.push({
        test: `API端点${endpoint}`,
        success: true,
        responseTime: apiTime,
        message: `API端点${endpoint}连接成功 (${apiTime}ms)`
      });
      console.log(`✅ API端点${endpoint}连接成功 (${apiTime}ms)`);
    } catch (apiError) {
      results.tests.push({
        test: `API端点${endpoint}`,
        success: false,
        error: apiError.message,
        message: `API端点${endpoint}连接失败: ${apiError.message}`
      });
      console.error(`❌ API端点${endpoint}连接失败:`, apiError.message);
    }
  }
  
  return results;
}

function testHttpRequest(url, method = 'GET', headers = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'User-Agent': 'local-test/1.0',
        ...headers
      }
    };
    
    const start = Date.now();
    const req = client.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        const time = Date.now() - start;
        resolve(time);
      });
    });
    
    req.on('error', reject);
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    req.end();
  });
}

async function compareWithCloudFunction() {
  console.log('🔄 开始对比测试...');
  
  // 本地测试
  const localResults = await testLocalConnection();
  
  console.log('\n📊 本地测试结果:');
  localResults.tests.forEach(test => {
    if (test.success) {
      console.log(`✅ ${test.test}: ${test.responseTime}ms`);
    } else {
      console.log(`❌ ${test.test}: ${test.error}`);
    }
  });
  
  console.log('\n💡 分析建议:');
  console.log('1. 请将此结果与云函数测试结果对比');
  console.log('2. 如果本地测试正常但云函数测试失败，说明是云函数网络环境问题');
  console.log('3. 如果两者都有问题，可能是aihubmix.com服务本身的问题');
  console.log('4. 关注响应时间差异，如果云函数明显更慢，可能需要优化网络配置');
  
  return localResults;
}

// 运行测试
if (require.main === module) {
  compareWithCloudFunction().then(() => {
    console.log('\n🎉 本地测试完成');
    console.log('\n📋 下一步操作:');
    console.log('1. 部署 test-aihubmix-connection 云函数');
    console.log('2. 在uniCloud控制台调用该云函数');
    console.log('3. 对比本地和云函数的测试结果');
    console.log('4. 根据对比结果确定具体的解决方案');
  }).catch(error => {
    console.error('❌ 测试失败:', error);
  });
}

module.exports = { testLocalConnection, compareWithCloudFunction };
